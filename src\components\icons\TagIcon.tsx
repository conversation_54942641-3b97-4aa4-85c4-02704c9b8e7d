import React from 'react';
import { IconProps } from './types';

const TagIcon: React.FC<IconProps> = () => {
  return (
    <svg
      width="23"
      height="23"
      viewBox="0 0 23 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_739_2281)">
        <path
          d="M11.3945 22.6426C8.45633 22.6426 5.694 21.4984 3.61633 19.4208C1.53875 17.3431 0.394531 14.5808 0.394531 11.6426C0.394531 8.70438 1.53875 5.94204 3.61633 3.86438C5.694 1.78679 8.45633 0.642578 11.3945 0.642578C14.3327 0.642578 17.0951 1.78679 19.1727 3.86438C21.2503 5.94204 22.3945 8.70438 22.3945 11.6426C22.3945 14.5808 21.2503 17.3431 19.1727 19.4208C17.0951 21.4984 14.3327 22.6426 11.3945 22.6426ZM11.3945 3.2207C6.75068 3.2207 2.97266 6.99873 2.97266 11.6426C2.97266 16.2864 6.75068 20.0645 11.3945 20.0645C16.0384 20.0645 19.8164 16.2864 19.8164 11.6426C19.8164 6.99873 16.0384 3.2207 11.3945 3.2207Z"
          fill="white"
        />
        <path
          d="M11.5664 17.6582C8.24939 17.6582 5.55078 14.9596 5.55078 11.6426C5.55078 8.32556 8.24939 5.62695 11.5664 5.62695C12.7718 5.62695 13.9351 5.98248 14.9307 6.65511C15.5206 7.0536 15.6758 7.85497 15.2772 8.44484C14.8786 9.03472 14.0773 9.18996 13.4875 8.79134C12.9197 8.40785 12.2554 8.20508 11.5664 8.20508C9.67097 8.20508 8.12891 9.74714 8.12891 11.6426C8.12891 13.538 9.67097 15.0801 11.5664 15.0801C12.2628 15.0801 12.8471 14.8741 13.3032 14.468C13.3921 14.3887 13.4774 14.3007 13.5563 14.2066C14.0138 13.6611 14.8268 13.5898 15.3724 14.0471C15.9179 14.5045 15.9893 15.3175 15.5319 15.8631C15.373 16.0525 15.2001 16.231 15.018 16.3932C14.0886 17.2208 12.8952 17.6582 11.5664 17.6582Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_739_2281">
          <rect
            width="22"
            height="22"
            fill="white"
            transform="translate(0.394531 0.642578)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default TagIcon;
