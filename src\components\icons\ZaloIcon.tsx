import React from 'react';
import { IconProps } from './types';

const ZaloIcon: React.FC<IconProps> = () => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_739_2249)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M11.4655 0.456299H13.6247C16.5901 0.456299 18.323 0.891843 19.8613 1.7166C21.3996 2.54134 22.6135 3.74604 23.429 5.28436C24.2537 6.82266 24.6893 8.55556 24.6893 11.5209V13.6709C24.6893 16.6363 24.2537 18.3692 23.429 19.9075C22.6042 21.4457 21.3996 22.6598 19.8613 23.4752C18.323 24.3 16.5901 24.7355 13.6247 24.7355H11.4748C8.50937 24.7355 6.77647 24.3 5.23817 23.4752C3.69989 22.6505 2.48593 21.4457 1.67045 19.9075C0.845695 18.3692 0.410156 16.6363 0.410156 13.6709V11.5209C0.410156 8.55556 0.845695 6.82266 1.67045 5.28436C2.4952 3.74604 3.69989 2.53208 5.23817 1.7166C6.76723 0.891843 8.50937 0.456299 11.4655 0.456299Z"
          fill="#0068FF"
        />
        <path
          opacity="0.12"
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M24.6894 13.3162V13.671C24.6894 16.6363 24.254 18.3692 23.4292 19.9075C22.6044 21.4458 21.3997 22.6598 19.8614 23.4753C18.3231 24.3001 16.5902 24.7356 13.6248 24.7356H11.475C9.04852 24.7356 7.44726 24.444 6.10259 23.8861L3.88574 21.6033L24.6894 13.3162Z"
          fill="white"
        />
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M4.13239 21.6827C5.2679 21.8082 6.68755 21.4844 7.69557 20.9951C12.0727 23.4144 18.9149 23.2989 23.0566 20.6483C23.2172 20.4074 23.3673 20.1568 23.5066 19.897C24.3344 18.3531 24.7715 16.6139 24.7715 13.6377V11.48C24.7715 8.50375 24.3344 6.76454 23.5066 5.22064C22.6881 3.67673 21.4698 2.46764 19.9258 1.63989C18.3819 0.81213 16.6427 0.375 13.6665 0.375H11.4995C8.96467 0.375 7.31917 0.694085 5.93661 1.30325C5.86104 1.37093 5.78689 1.43976 5.7143 1.50974C1.6581 5.41987 1.34977 13.8957 4.78928 18.4999C4.79312 18.5067 4.79736 18.5136 4.80204 18.5205C5.33218 19.3017 4.82064 20.669 4.02078 21.4688C3.89058 21.5897 3.93708 21.6641 4.13239 21.6827Z"
          fill="white"
        />
        <path
          d="M10.3816 8.68481H5.62778V9.70417H8.92675L5.67407 13.7353C5.57215 13.8835 5.49805 14.0225 5.49805 14.3376V14.5971H9.98315C10.2056 14.5971 10.3909 14.4117 10.3909 14.1893V13.6426H6.92514L9.98315 9.80609C10.0295 9.75051 10.1129 9.64859 10.15 9.60225L10.1685 9.57444C10.3446 9.31496 10.3816 9.09255 10.3816 8.82384V8.68481Z"
          fill="#0068FF"
        />
        <path
          d="M16.4327 14.5971H17.1092V8.68481H16.0898V14.2542C16.0898 14.4396 16.2381 14.5971 16.4327 14.5971Z"
          fill="#0068FF"
        />
        <path
          d="M12.9485 10.001C11.6697 10.001 10.6318 11.0389 10.6318 12.3177C10.6318 13.5965 11.6697 14.6344 12.9485 14.6344C14.2274 14.6344 15.2653 13.5965 15.2653 12.3177C15.2745 11.0389 14.2367 10.001 12.9485 10.001ZM12.9485 13.6799C12.1979 13.6799 11.5863 13.0683 11.5863 12.3177C11.5863 11.5671 12.1979 10.9555 12.9485 10.9555C13.6991 10.9555 14.3108 11.5671 14.3108 12.3177C14.3108 13.0683 13.7084 13.6799 12.9485 13.6799Z"
          fill="#0068FF"
        />
        <path
          d="M20.1204 9.96387C18.8323 9.96387 17.7852 11.011 17.7852 12.2991C17.7852 13.5872 18.8323 14.6344 20.1204 14.6344C21.4085 14.6344 22.4556 13.5872 22.4556 12.2991C22.4556 11.011 21.4085 9.96387 20.1204 9.96387ZM20.1204 13.6799C19.3605 13.6799 18.7489 13.0683 18.7489 12.3084C18.7489 11.5485 19.3605 10.9369 20.1204 10.9369C20.8803 10.9369 21.4919 11.5485 21.4919 12.3084C21.4919 13.0683 20.8803 13.6799 20.1204 13.6799Z"
          fill="#0068FF"
        />
        <path
          d="M14.728 14.597H15.2748V10.1304H14.3203V14.1986C14.3203 14.4117 14.5056 14.597 14.728 14.597Z"
          fill="#0068FF"
        />
      </g>
      <defs>
        <clipPath id="clip0_739_2249">
          <rect
            width="24.4415"
            height="24.4415"
            fill="white"
            transform="translate(0.329102 0.375)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ZaloIcon;
