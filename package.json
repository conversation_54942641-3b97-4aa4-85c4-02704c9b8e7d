{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "prod": "docker compose up -d --build"}, "dependencies": {"@fortawesome/fontawesome-free": "^7.0.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "antd": "^5.25.3", "aos": "^3.0.0-beta.6", "apexcharts": "^4.7.0", "atomic-spinner": "^1.3.13", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^4.1.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "lodash.debounce": "^4.0.8", "lucide-react": "^0.344.0", "qrcode.react": "^4.2.0", "qs": "^6.14.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-router-dom": "^6.22.3", "react-slick": "^0.30.3", "react-swipeable": "^7.0.2", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.18", "tailwind-scrollbar": "^4.0.2", "zod": "^3.25.36", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/aos": "^3.0.7", "@types/lodash.debounce": "^4.0.9", "@types/node": "^24.0.0", "@types/qrcode.react": "^1.0.5", "@types/qs": "^6.14.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-slick": "^0.23.13", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "typescript-eslint": "^8.3.0", "vite": "^6.3.5"}}