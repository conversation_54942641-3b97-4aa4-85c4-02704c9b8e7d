import React from 'react';
import { IconProps } from './types';

const YoutubeIcon: React.FC<IconProps> = () => {
  return (
    <svg width="29" height="23" viewBox="0 0 29 23" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M27.6945 3.95593C27.3763 2.58942 26.442 1.51554 25.2532 1.14974C23.1006 0.486084 14.465 0.486084 14.465 0.486084C14.465 0.486084 5.82952 0.486084 3.67688 1.14974C2.48806 1.51554 1.55381 2.58942 1.23557 3.95593C0.658203 6.43027 0.658203 11.5958 0.658203 11.5958C0.658203 11.5958 0.658203 16.7614 1.23557 19.2358C1.55381 20.6023 2.48806 21.6762 3.67688 22.0419C5.82952 22.7056 14.465 22.7056 14.465 22.7056C14.465 22.7056 23.1006 22.7056 25.2532 22.0419C26.442 21.6762 27.3763 20.6023 27.6945 19.2358C28.2719 16.7614 28.2719 11.5958 28.2719 11.5958C28.2719 11.5958 28.2696 6.43027 27.6945 3.95593Z" fill="#FF0000"/>
    <path d="M11.7012 16.3573L18.8751 11.5968L11.7012 6.83618V16.3573Z" fill="white"/>
    </svg>    
  );
};

export default YoutubeIcon; 
