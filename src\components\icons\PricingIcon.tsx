import React from 'react';
import { IconProps } from './types';

const PricingIcon: React.FC<IconProps> = () => {
  return (
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" clipRule="evenodd" d="M4.98382 8.28379C4.98382 6.46119 6.46059 4.98442 8.28319 4.98297H9.78476C10.6559 4.98297 11.4906 4.63712 12.1093 4.02424L13.1585 2.97358C14.4441 1.68068 16.5338 1.67484 17.8267 2.96044L17.8282 2.9619L17.8413 2.97358L18.8919 4.02424C19.5107 4.63858 20.3454 4.98297 21.2165 4.98297H22.7166C24.5392 4.98297 26.0175 6.45973 26.0175 8.28379V9.78244C26.0175 10.6536 26.3619 11.4898 26.9762 12.1085L28.0269 13.1591C29.3198 14.4447 29.327 16.5344 28.0414 17.8273L28.04 17.8288L28.0269 17.8419L26.9762 18.8925C26.3619 19.5098 26.0175 20.3445 26.0175 21.2157V22.7172C26.0175 24.5398 24.5407 26.0166 22.7181 26.0166H21.2136C20.3424 26.0166 19.5063 26.3625 18.889 26.9768L17.8384 28.026C16.5542 29.3189 14.466 29.3262 13.1731 28.0435C13.1717 28.0421 13.1702 28.0406 13.1688 28.0391L13.1556 28.026L12.1064 26.9768C11.4892 26.3625 10.653 26.0181 9.78184 26.0166H8.28319C6.46059 26.0166 4.98382 24.5398 4.98382 22.7172V21.2128C4.98382 20.3416 4.63798 19.5069 4.02364 18.8896L2.97443 17.839C1.68154 16.5548 1.67424 14.4666 2.95838 13.1737C2.95838 13.1723 2.95984 13.1708 2.9613 13.1694L2.97443 13.1562L4.02364 12.1056C4.63798 11.4868 4.98382 10.6522 4.98382 9.77952V8.28379Z" stroke="#1E8CFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M11.7493 19.2512L19.2498 11.7506" stroke="#28E196" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M18.8618 18.8751H18.8751" stroke="#28E196" strokeWidth="2.89286" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M12.125 12.1251H12.1383" stroke="#28E196" strokeWidth="2.89286" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
  );
};

export default PricingIcon; 
