import React from 'react';
import { IconProps } from './types';

const EnglishIcon: React.FC<IconProps> = () => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1_555)">
        <path
          d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z"
          fill="#F0F0F0"
        />
        <path
          d="M4.96113 9.38834C3.07591 11.8411 1.65438 14.6681 0.82666 17.7393H13.3121L4.96113 9.38834Z"
          fill="#0052B4"
        />
        <path
          d="M47.173 17.7393C46.3453 14.6682 44.9236 11.8412 43.0385 9.3884L34.6877 17.7393H47.173Z"
          fill="#0052B4"
        />
        <path
          d="M0.82666 30.261C1.65447 33.3321 3.076 36.1591 4.96113 38.6118L13.3118 30.261H0.82666Z"
          fill="#0052B4"
        />
        <path
          d="M38.6116 4.96136C36.1588 3.07614 33.3319 1.65461 30.2607 0.826797V13.3121L38.6116 4.96136Z"
          fill="#0052B4"
        />
        <path
          d="M9.38818 43.0387C11.841 44.9239 14.668 46.3454 17.7391 47.1732V34.688L9.38818 43.0387Z"
          fill="#0052B4"
        />
        <path
          d="M17.739 0.826797C14.6679 1.65461 11.8409 3.07614 9.38818 4.96127L17.739 13.312V0.826797Z"
          fill="#0052B4"
        />
        <path
          d="M30.2607 47.1732C33.3318 46.3454 36.1588 44.9239 38.6115 43.0388L30.2607 34.688V47.1732Z"
          fill="#0052B4"
        />
        <path
          d="M34.6877 30.261L43.0385 38.6119C44.9236 36.1592 46.3453 33.3321 47.173 30.261H34.6877Z"
          fill="#0052B4"
        />
        <path
          d="M47.7968 20.8696H27.1306H27.1305V0.203156C26.1057 0.06975 25.061 0 24 0C22.9388 0 21.8943 0.06975 20.8696 0.203156V20.8694V20.8695H0.203156C0.06975 21.8943 0 22.939 0 24C0 25.0612 0.06975 26.1057 0.203156 27.1304H20.8694H20.8695V47.7968C21.8943 47.9303 22.9388 48 24 48C25.061 48 26.1057 47.9303 27.1304 47.7968V27.1306V27.1305H47.7968C47.9303 26.1057 48 25.0612 48 24C48 22.939 47.9303 21.8943 47.7968 20.8696Z"
          fill="#D80027"
        />
        <path
          d="M30.261 30.261L40.9706 40.9706C41.4632 40.4782 41.933 39.9635 42.3814 39.4298L33.2124 30.2609H30.261V30.261Z"
          fill="#D80027"
        />
        <path
          d="M17.739 30.261H17.7388L7.0293 40.9705C7.52167 41.4631 8.03645 41.933 8.57008 42.3813L17.739 33.2122V30.261Z"
          fill="#D80027"
        />
        <path
          d="M17.739 17.7393V17.7391L7.0294 7.02938C6.53684 7.52176 6.06696 8.03654 5.61865 8.57016L14.7877 17.7392L17.739 17.7393Z"
          fill="#D80027"
        />
        <path
          d="M30.261 17.7393L40.9707 7.02947C40.4783 6.53691 39.9636 6.06703 39.4299 5.61881L30.261 14.7878V17.7393Z"
          fill="#D80027"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_555">
          <rect width="48" height="48" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default EnglishIcon;
