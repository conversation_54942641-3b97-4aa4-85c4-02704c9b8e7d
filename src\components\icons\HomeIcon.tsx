import React from 'react';
import { IconProps } from './types';

const HomeIcon: React.FC<IconProps> = () => {
  return (
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 15.5C4 14.7342 4 14.3513 4.0987 13.9987C4.18614 13.6864 4.32982 13.3926 4.5227 13.1318C4.74045 12.8374 5.04269 12.6023 5.64719 12.1321L14.6903 5.09864C15.1587 4.7343 15.3929 4.55214 15.6515 4.48211C15.8797 4.42032 16.1203 4.42032 16.3485 4.48211C16.6071 4.55214 16.8413 4.7343 17.3097 5.09864L26.3528 12.1321C26.9573 12.6023 27.2596 12.8374 27.4773 13.1318C27.6702 13.3926 27.8139 13.6864 27.9013 13.9987C28 14.3513 28 14.7342 28 15.5V23.7333C28 25.2268 28 25.9735 27.7094 26.544C27.4537 27.0457 27.0457 27.4537 26.544 27.7093C25.9735 28 25.2268 28 23.7333 28H8.26667C6.77319 28 6.02646 28 5.45603 27.7093C4.95426 27.4537 4.54631 27.0457 4.29065 26.544C4 25.9735 4 25.2268 4 23.7333V15.5Z" stroke="#1E8CFF" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          <path d="M10.667 22.6666H21.3337" stroke="#28E196" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
  );
};

export default HomeIcon; 
