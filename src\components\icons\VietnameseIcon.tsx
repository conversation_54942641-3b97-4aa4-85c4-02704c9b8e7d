import React from 'react';
import { IconProps } from './types';

const VietnameseIcon: React.FC<IconProps> = () => {
  return (
    <svg width="40" height="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_1_573)">
    <path d="M24 48C37.2548 48 48 37.2548 48 24C48 10.7452 37.2548 0 24 0C10.7452 0 0 10.7452 0 24C0 37.2548 10.7452 48 24 48Z" fill="#D80027"/>
    <path d="M24.0001 12.5217L26.5902 20.4932H34.9719L28.1909 25.4197L30.781 33.3913L24.0001 28.4646L17.2191 33.3913L19.8093 25.4197L13.0283 20.4932H21.4099L24.0001 12.5217Z" fill="#FFDA44"/>
    </g>
    <defs>
    <clipPath id="clip0_1_573">
    <rect width="48" height="48" fill="white"/>
    </clipPath>
    </defs>
    </svg>    
  );
};

export default VietnameseIcon; 
