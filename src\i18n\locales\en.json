{"language": {"vi": "Vietnamese", "en": "English"}, "readMore": "Read more", "subTextNavbar": "Services", "authen": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "profile": {"menu": {"home": "Home", "points": "Points", "account": "Account", "voucher": "Voucher", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "info": "General Info", "changePassword": "Change Password", "giveHistory": "Point Giving History", "receiveHistory": "Point Receiving History"}, "form": {"info": "Information", "username": "Username", "email": "Email", "phone": "Phone number", "points": "Points", "referralCode": "Referral Code", "percentage": "Percentage Discount", "copy": "Copy", "saveChanges": "Save changes"}, "avatar": {"avatarHint1": "Allowed image types: *.jpeg, *.jpg, *.png, *.gif", "avatarHint2": "Maximum file size: 3 Mb", "changeAvatar": "Change avatar", "loading": "Processing..."}, "history": {"username": "Sender", "email": "Sender <PERSON><PERSON>", "points": "Points", "toUser": "Recipient", "toUserEmail": "Recipient Email", "accountType": "Account Type", "account": {"main": "Main", "sub": "Sub"}, "mainAccount": "Main Account", "subAccount": "Sub Account"}, "givePointModal": {"title": "Give Points", "okText": "Confirm", "cancelText": "Cancel", "rule": "The point must be the positive number", "placeholder": "Enter points"}, "changeTypeAccount": {"title": "Confirm Account Type Change", "okText": "Confirm", "cancelText": "Cancel", "question": "Which account type do you want to switch to?", "placeholder": "Select account type", "main": "Main Account (MAIN)", "sub": "Sub Account (SUB)"}, "button": {"givePoint": "Give points", "changeType": "Change account type"}, "password": {"old": "Old Password", "new": "New Password", "confirm": "Confirm New Password", "placeholder": {"old": "Old Password", "new": "New Password", "confirm": "Confirm New Password"}, "button": "Save Changes", "validate": {"oldRequired": "Old password is required", "newMin": "New password must be at least 6 characters", "confirmMismatch": "Passwords do not match", "sameAsOld": "New password must not be the same as old password"}, "toast": {"noUserId": "User ID not found in localStorage!", "success": "Password updated successfully!", "defaultError": "An error occurred while updating the password!", "wrongPassword": "Incorrect current password"}}}, "nav": {"home": "Home", "accounts": "Account List", "pricing": "Pricing", "rented": "Account management", "topup": "Top-up", "history": "Transaction History", "support": "Support", "policy": "Policy"}, "mainHeading": {"title": "Advertising Rental Solution", "titleBot": "AGENCY ADVERTISING ACCOUNTS", "subtitle": "AKAds accompanies businesses to optimize profits", "subtitleBot": "Through a trusted ad account system and high-quality creative materials", "textButton": "Rent an account now"}, "whyChoose": {"button": "WHY CHOOSE AKA ADS?", "headingLine1": "Stable account", "highlight": "Boost sales", "headingLine2": "effectively"}, "strongAccount": {"titleLine1": "Strong account", "titleLine2": "Boost advertising quickly", "descLine1": "High-quality account", "descLine2": "High payment threshold.", "descLine3": "Fast approval, effective delivery", "descLine4": "No budget limit concerns."}, "streamlinedProcess": {"title": "Streamlined management process", "descLine1": "Flexible selection – strong accounts,", "descLine2": "ready backups, uninterrupted advertising"}, "systemSecurity": {"titleLine1": "Integrated system", "titleLine2": "with modern security", "descLine1": "100% security commitment,", "descLine2": "no worries about hacking.", "descLine3": "Transparent transactions,", "descLine4": "no hidden fees."}, "active": {"title": "Quick activation, seamless<br/>operation", "descLine1": "Automatic top-up, easy denomination selection and order creation.", "descLine2": "The system automatically updates accurately<br/>for each account."}, "payment": {"title": "Flexible, easy<br/>payment", "descLine1": "Integrated with multiple payment methods.", "descLine2": "Automatically processes transactions, fully secure,<br/> no disruptions."}, "blog": {"titleLine1": "Stay updated with the latest news", "titleLine2": "<span>Markets & Platforms</span>"}, "news": {"1": {"title": "Rent Facebook Advertising Accounts", "excerpt": "A safe and effective solution for businesses to rent Facebook advertising accounts."}, "2": {"title": "Facebook Ad Accounts: A Beginner’s Complete Guide", "excerpt": "In today’s digital era, promoting products and services on social platforms is more important than ever. Facebook has over 2.9 billion monthly active users."}, "3": {"title": "Run Facebook Ads on a Budget", "excerpt": "Strategies to optimize your advertising budget when running Facebook ads at the lowest cost."}, "4": {"title": "Optimizing Facebook Ads for Small Businesses", "excerpt": "A step-by-step guide to optimizing Facebook ads for small businesses with limited budgets."}}, "footer": {"companyName": "AKA Media Agency", "companyName2": "AKA Media technology", "slogan": "Comprehensive digital solutions", "address": "4th Floor, Viet Huong Building,", "address2": "<PERSON><PERSON>, Thanh Tri, Hanoi, Vietnam", "serviceTitle": "SERVICES", "services": {"rentBM": "BM rental", "rentAdAccount": "Ad account rental", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "accountManagement": "Account management"}, "contactTitle": "CONTACT", "supportTitle": "SUPPORT", "support": {"faq": "FAQ", "terms": "Terms of use", "privacy": "Privacy policy"}, "footerBot": "Aka Media, 2025. Reserve all rights."}, "price": {"title": "SERVICE QUOTE", "title2": "Account", "title2sub": "rental package", "title3": "Advertisement", "fee": "Service fee", "rent": "Rent now", "contact": "Contact"}, "highlightProject": {"title": "HIGHLIGHT PROJECT", "title2": "Thousands of businesses", "title3": "Trust and choice", "content": "Developed by AKA Media - a pioneer in the field of digital advertising, AKAds provides a reputable Facebook account rental service, proudly accompanying 5,000+ customers over the past 8 years, providing effective, stable and cost-optimized advertising solutions."}, "modalHomepage": {"login": {"title": "Log In", "subTitle": "Welcome back to AKA Media", "subTitle2": "Enter email to receive password reset link", "password": "Password", "forgotPassword": "Forgot password?", "footer1": "You do not have an account yet?", "footer2": "Create account now", "validateEmail": "Please enter email", "validatePassword": "Please enter password", "error": "An error occurred, please try again.", "toastSuccess": "Password reset email has been sent!", "backToLogin": "Back to login", "sendRequest": "Send request"}, "register": {"title": "Create New Account", "subtitle": "Sign up now to use AKAds services.", "name": "Full Name", "phone": "Phone", "password": "Password", "confirmPassword": "Confirm password", "codeIntroduce": "Enter referral code", "errorName": "Full name is required", "errorEmail": "Email is required", "errorEmail2": "Invalid email", "errorPhone": "Phone is required", "errorPhone2": "Phone number must be 10 digits", "errorPassword": "Password is required", "notePassword": "Password must be at least 6 characters.", "notePassword2": "Re-enter password to confirm", "errorConfirmPassword": "Confirmation password does not match", "buttonRegister": "Register", "hadAccount": "Already have an account?", "login": "Log In", "failedRegister": "Registration failed", "existedEmail": "Existed email", "existedEmail2": "Email already exists. Please enter another email.", "errorCommon": "An error occurred during registration."}, "forgotPassword": {"createNew": "Create new password", "enterNewPass": "Please enter a new password for your account", "strongPass": {"strong1": "Strong length", "strong2": "Weak", "strong3": "Medium", "strong4": "Quite", "strong5": "Strong", "strong6": "At least 8 characters", "strong7": "At least 1 uppercase letter", "strong8": "At least 1 lowercase letter", "strong9": "At least 1 number", "strong10": "At least 1 special character"}, "matchNotPass": "Password do not match", "passIsRequired": "Password is required", "matchPass": "Password match", "accountSecurity": "Account Security", "accountSecurity2": "The new password will be applied immediately. Please use a strong password to protect your account.", "updatePass": "Update password", "successToast": "Password reset successful!", "failToast": "An error occurred while resetting your password.", "successReset": "Password reset successful!", "successReset2": "Your password has been updated. You can log in with the new password.", "successReset3": "Sign in now"}}, "loginPage": {"successLogin": "Login successful", "account": "Account", "registerNow": "Register now", "allRightReserved": "© AKA Media, 2025. All rights reserved."}, "registerPage": {"successRegister": "Registration successful. Your account has been created!"}, "supportPage": {"header": {"header1": "Support Center", "header2": "Smart support request management"}, "filter": {"status": {"status1": "All status", "status2": "Pending processing", "status3": "Processing", "status4": "Resolved"}, "search": {"placeHolder": "Search by title, content,...", "create": "Create request"}}, "main": {"main1": "No support requests yet", "main2": "Your system is running smoothly. Please create a support request when needed.", "main3": "Create first request"}, "hadData": {"seeDetail": "Detail", "deleteRequest": "Delete", "low": "low", "medium": "medium", "high": "high", "urgent": "urgent", "pending": "pending", "inProgress": "in-progress", "resolved": "resolved", "closed": "closed", "loading": "Loading...", "loadMore": "Load more"}}, "supportPageDetail": {"head": {"back": "Back to list"}, "main": {"userInfor": "User Information", "detailRequest": "Detail request", "category": "category", "department": "department", "description": "description", "file": "Detail file", "time": "Time information", "createTime": "Create time", "updateTime": "Update time", "box": "Support Conversation", "message": "message", "message2": "There are no support requests yet", "message3": "When there are messages, the conversation will appear", "message4": "Enter your message", "send": "Send"}}, "createRequest": {"header": {"header1": "Back to list", "header2": "Submit a support request", "header3": "We are available to support you 24/7"}, "form": {"name": "Full name", "name2": "Enter your first and last name", "phone": "Phone", "title": "Title", "title2": "Summarize your problem", "department": "Department", "department2": "Select department", "department3": "Technical Support", "department4": "Business", "department5": "HR", "priority": "Priority level", "priority1b": "Select priority level", "priority2": "Low", "priority3": "Medium", "priority4": "High", "priority5": "<PERSON><PERSON>", "category": "Category", "category2": "Select category", "category3": "Account", "category4": "Pay", "category5": "Recover", "category6": "Other", "content": "Detailed content", "content2": "Describe in detail the problem you are having.", "content3": "Minimum 10 characters", "upload": "Attach file", "fileTooLarge": "The file {{fileName}} is too large. Maximum size is 5MB.", "fileNotSupport": "File {{fileName}} is not supported. Only accepted: JPG, PNG, GIF, PDF, TXT.", "dragFile": "Drag and drop files here or click to select", "dragFile2": "Support: JPG, PNG, GIF, PDF, TXT (up to 5MB)", "chooseFile": "Select file", "send1": "Sending...", "send2": "Send request", "successToast": "Support request and email sent successfully!", "error1": "One or both requests failed.", "error2": "An error occurred while sending the request. Please try again.", "successMessage": "Sent successfully!", "successMessage2": "Your support request has been submitted. We will respond as soon as possible.", "successMessage3": "Submit another request"}}, "404": {"text": "Page not found", "text2": "Sorry your page we can't not found", "text3": "Go Back"}, "marketplacePage": {"title": "BM Ad Accounts", "syncAccounts": "Sync Accounts", "createBM": "Create BM Account", "bmList": "BM List", "adAccountsWithCard": "Ad Accounts With Card", "adAccountsWithoutCard": "Ad Accounts Without Card", "rentedAccounts": "Rented Accounts", "adAccounts": "All Ad Accounts", "noAccountsWithoutCard": "No accounts without card.", "noRentedAccounts": "No accounts are currently being rented.", "fields": {"id": "Account ID", "accountStatus": "Account Status", "amountSpent": "Amount Spent", "balance": "Pay", "currency": "<PERSON><PERSON><PERSON><PERSON>", "name": "Account Name", "spendCap": "Spend Cap", "end_advertiser_name": "Owner", "statusRented": "Rental Status", "spendLimit": "Spend Limit", "noteAka": "Note", "active": "Active"}, "errors": {"fetchAdAccounts": "Error fetching ad account list", "syncFailed": "Sync failed. Please try again.", "deleteBMFailed": "Cannot delete BM account. Please try again later."}, "success": {"syncComplete": "Account synchronization successful", "deleteBM": "BM account deleted successfully"}}, "cardDetailModal": {"title": "Account Details", "basicInfo": "Basic Information", "financialInfo": "Financial Information", "additionalInfo": "Additional Information", "accountStatus": {"active": "Active", "inactive": "Inactive"}, "yes": "Yes", "no": "No", "noLimit": "No limit", "account_id": "ID Account", "accountTypeBusiness": "Business", "type": "Type", "rentNow": "Rent now"}, "adAccountCard": {"personal": "Personal", "bm": "BM", "visa": "Visa", "highLimit": "High Limit", "lowLimit": "Low Limit", "available": "Available", "rented": "Rented", "unavailable": "Unavailable", "adAccountType": "Ad Account Type", "withCard": "With Card", "withoutCard": "Without Card", "accountNumber": "Account Number", "noCardInfo": "No card information", "spendLimit": "Spend Limit", "noLimit": "No limit", "highQualityAccount": "High quality BM account, identity verified", "rentNow": "Rent Now", "viewDetails": "View Details"}, "filter": {"priceRange": "Price Range", "accountType": "Account Type", "withCard": "With Card", "withoutCard": "Without Card", "noLimit": "No Limit", "close": "Reset Filter", "viewResults": "View Results", "minimum": "Minimum", "maximum": "Maximum"}, "rentalsPage": {"title": "Rented Accounts", "loading": "Loading data...", "tabs": {"processing": "Processing", "success": "Success", "failed": "Failed", "completed": "Completed", "all": "All"}, "status": {"processing": "Processing", "success": "Success", "failed": "Failed", "completed": "Completed"}, "startDate": "Start Date", "requestedLimit": "Requested Limit", "spentBudget": "Spent Budget", "noLimit": "No limit", "bmId": "BM ID", "rentalPeriod": "Rental Period", "spendLimit": "Spend Limit", "spent": "Spent", "remaining": "Remaining", "refundAvailable": "Refund Available", "refundUnusedPoints": "Refund unused points", "buttons": {"disable": "Disable", "processing": "Processing", "rentNow": "Rent Now"}, "emptyState": {"processing": "You don't have any accounts being rented.", "success": "You don't have any active accounts.", "all": "You haven't rented any accounts yet."}, "modal": {"title": "Account Details", "loading": "Loading account details...", "basicInfo": "Basic Information", "accountId": "Account ID", "accountName": "Account Name", "accountStatus": "Account Status", "statusActive": "Active", "statusInactive": "Inactive", "currency": "<PERSON><PERSON><PERSON><PERSON>", "financialInfo": "Financial Information", "amountSpent": "Amount Spent", "balance": "Balance", "spendCap": "Spend Cap", "noLimit": "No limit", "additionalInfo": "Additional Information", "createdDate": "Created Date", "timezone": "Timezone", "accountType": "Account Type", "personal": "Personal", "business": "Business", "bmName": "BM Name", "notAvailable": "N/A", "close": "Close", "loadError": "Unable to load account details. Please try again later.", "retry": "Retry"}, "notifications": {"setupInProgress": "Please wait a moment for the system to set up", "disableSuccess": "Disabled successfully!", "disableFailed": "Disable failed!", "connectionError": "System connection error. Please try again!"}}, "paymentPage": {"paymentGateway": "Payment Gateway", "currentBalance": "Current Balance", "points": "points", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositToSystem": "Deposit to System", "enterAmount": "Enter Amount", "selectDenomination": "Select Denomination", "amountValidation": "Amount must be at least 50,000 VND and in multiples of 1,000 VND", "amountMustBeAtLeast": "Amount must be at least 50,000 VND", "transferInfo": "Transfer Information", "bank": "Bank", "acbAsia": "Asia Commercial Bank", "accountNumber": "Account Number", "accountName": "Account Name", "transferContent": "Transfer Content", "amount": "Amount", "back": "Back", "minutes": "minutes", "createDepositOrder": "Create Deposit Order", "automaticDepositNote": "The system will automatically update your balance after the transfer is completed", "qrCode": "QR Code for Payment", "depositInstructions": "Deposit Instructions", "generatingQR": "Generating QR code...", "scanQRInstructions": "Scan this QR code with your banking app to make the payment automatically", "qrExpires": "QR code expires in", "step1Title": "Enter Amount", "step1Description": "Choose or enter the amount you want to deposit", "step2Title": "Create Deposit Order", "step2Description": "Click the button to generate payment information", "step3Title": "Make the Transfer", "step3Description": "Transfer the exact amount with the provided content", "step4Title": "Automatic Confirmation", "step4Description": "Your balance will be updated automatically after the transfer is confirmed", "selectPaymentMethod": "Select Payment Method", "vietnameseDong": "Vietnamese Dong (VND)", "bankTransfer": "Bank Transfer", "usDollar": "US Dollar (USD)", "cryptocurrency": "Cryptocurrency", "comingSoon": "Coming Soon", "featureInDevelopment": "This feature is in development", "changePaymentMethod": "Change Payment Method", "copied": "<PERSON>pied", "copiedToClipboard": "Copied to clipboard", "error": "Error", "enterValidAmount": "Please enter an even amount of 1 million or more", "depositSuccess": "Deposit Order Created", "pleaseTransfer": "Please transfer the exact amount with the provided content", "errorOccurred": "<PERSON><PERSON><PERSON>urred", "cannotCreateDeposit": "Cannot create deposit order. Please try again later", "remainPoint": "AKA Ads Wallet Balance", "coming": "Coming soon", "confirm": "Confirm", "point": "points", "instructions": "Deposit instructions", "instruction1": "Select or enter the amount you want to deposit", "instruction2": "Click the “Create Deposit Order” button to create payment information", "instruction3": "Transfer the correct amount with the content provided", "instruction4": "Your AKA Ads Wallet balance will be updated automatically once the transfer is confirmed.", "paypal": {"title": "Pay with", "description": "Secure and convenient international payment method", "enterAmount": "Enter amount in USD", "redirecting": "Redirecting to PayPal...", "payWithPaypal": "Pay with PayPal", "securePayment": "Secure payment processed by PayPal", "faq": {"title": "Frequently Asked Questions", "q1": "What is the minimum deposit amount?", "a1": "The minimum deposit amount is $5 USD.", "q2": "How long does it take to process the payment?", "a2": "PayPal payments are usually processed instantly, but may take up to 24 hours in some cases.", "q3": "What is the exchange rate?", "a3": "We use the current market exchange rate at the time of transaction, with a small processing fee.", "q4": "Can I get a refund?", "a4": "Refunds can be processed within 30 days of the transaction. Please contact our support team."}, "note": {"title": "Important Note", "content": "Please ensure your PayPal account is verified to avoid any payment issues. International transaction fees may apply depending on your country and bank."}}, "status": {"processing": "Processing Payment", "pleaseWait": "Please wait while we confirm your payment...", "success": "Payment Successful", "successMessage": "Your balance has been updated successfully!", "failed": "Payment Failed", "failedMessage": "We couldn't confirm your payment. Please try again or contact support.", "tryAgain": "Try Again"}}, "adminTransactions": {"title": {"moneyTransactions": "Money Transactions Management", "pointsTransactions": "Points Transactions Management"}, "search": {"placeholder": "Search by ID, amount, description..."}, "buttons": {"reset": "Reset", "sync": "Sync", "details": "Details", "edit": "Edit"}, "table": {"headers": {"id": "ID", "amount": "Amount", "date": "Date", "status": "Status", "actions": "Actions"}, "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed"}}, "pagination": {"showing": "Showing", "of": "of", "items": "items"}, "emptyState": "No transactions found!"}, "rentModal": {"title": "Rent account", "infoAlert": "You will attach the card to the ad account yourself.", "accountLabel": "Account:", "bmIdLabel": "Your BM ID", "bmIdPlaceholder": "Example: *********", "bmIdError": "BM ID must be a valid ID and cannot be empty", "bmIdHelper": "BM ID is needed for access permission", "limitLabel": "Requested spending limit (VND)", "limitPlaceholder": "0 VND", "limitError": "Amount must be greater than 10,000 VND", "limitHelper": "Amount must be greater than 10,000 VND", "limitDisplay": "Limit:", "rentalLabel": "Rental period (7-31 days)", "rentalError": "Rental period must be between 7 and 31 days.", "rentalSelectError": "Please select a rental period.", "paymentMethodLabel": "Select payment method", "voucherLabel": "Select voucher", "noVoucher": "-- Do not use voucher --", "paymentDetails": "Payment details", "serviceNote": "* Service fees are based on account limits. Refer to pricing or support for more info.", "limitFee": "Limit increase fee", "voucherDiscount": "Voucher discount:", "serviceFee": "Service fee:", "totalPayment": "Total payment:", "userBalance": "Balance:", "notEnoughBalance": "Insufficient balance to rent this account. Please top up.", "cancel": "Cancel", "confirm": "Confirm rental", "requireBmId": "Enter your BM ID", "verifyInput": {"verifyInput1": "BM ID must be a string ID and cannot be empty", "verifyInput2": "BM ID must be a numeric string", "verifyInput3": "Spending limit must be a number", "verifyInput4": "Spending limit must be greater than 10,000 VND", "verifyInput5": "The limit cannot exceed the balance.", "verifyInput6": "point"}, "runningTime": "Running time", "applyVoucher": "Apply voucher", "selectVoucher": "Select voucher", "remainWallet": "AKA Ads Wallet Balance:", "spendingLimit": "Required spending limit:", "discountVoucher": "Discount voucher:", "note": {"note1": "Service fees are calculated based on the account spending limit. Please refer to", "note2": "Price list", "note3": "or contact", "note4": "Support"}}, "notificationSidebar": {"title": "Notifications", "count": "{{count}} notifications", "markAllRead": "Mark all as read", "noNotifications": "No notifications", "noNotificationsDesc": "New notifications will appear here", "markRead": "<PERSON> as read", "delete": "Delete", "deleting": "Deleting..."}, "payment_modal": {"title": "Add Payment Card", "card_name": "Cardholder Name", "card_name_placeholder": "NGUYEN VAN ANH", "card_number": "Card Number", "card_number_placeholder": "1234 5678 9012 3456", "expiry": "MM/YY", "cvv": "CVV", "terms": "I agree to the terms and policies", "save": "Save", "errors": {"card_name_required": "Please enter the cardholder name.", "card_name_invalid": "Name must contain only letters and spaces.", "card_name_max": "Cardholder name must not exceed 40 characters.", "card_number_required": "Please enter the card number.", "card_number_invalid": "Card number must be 16 digits.", "expiry_required": "Please enter expiry date.", "expiry_invalid": "Enter a valid format MM/YY (e.g., 12/25).", "cvv_required": "Please enter CVV.", "cvv_invalid": "CVV must be 3 or 4 digits.", "agree_terms": "Please agree to the terms."}}, "payment_method": {"title": "Select Payment Method", "vnd": "VND (Internet Banking)", "usd": "USD (PayPal)", "visa": "Visa", "mastercard": "MasterCard", "coming_soon": "Coming soon...", "bank_icon_alt": "Bank icon", "paypal_icon_alt": "PayPal icon", "visa_icon_alt": "Visa icon", "mastercard_icon_alt": "MasterCard icon", "developing": "This feature is under development"}, "transaction": {"title": "Point Exchange Transaction Management", "searchPlaceholder": "Search by ID, Account", "searchButton": "Search", "syncButton": "Sync Account", "detailsTitle": "Account Details", "tableHeaders": {"id": "ID", "points": "Points Used", "service": "Service Type", "account": "Target Account", "status": "Status", "createdAt": "Created At", "description": "Description"}}, "common": {"sort": {"asc": "ASC", "desc": "DESC"}, "button": {"confirm": "Confirm", "cancel": "Cancel", "close": "Close"}, "noti": {"success": "Success", "error": "Failed"}}, "account_card": {"bm_id": "BM ID", "start_date": "Start date", "end_date": "End date", "spend_limit": "Spend limit", "amount_spent": "Amount spent", "remaining": "Remaining", "cancel": "Cancel rental", "support": "Contact Support", "confirm_title": "Are you sure you want to cancel this account?", "confirm_content": "This action cannot be undone.", "confirm_ok": "Confirm", "confirm_cancel": "Cancel", "status": {"processing": "Processing", "success": "Success", "faild": "Faild", "complete_remove": "Rented"}}}