import React from 'react';
import { IconProps } from './types';

const Percent: React.FC<IconProps> = () => {
  return (
    <svg
      width="50"
      height="50"
      viewBox="0 0 50 50"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M47.6237 20.0812C45.3154 18.2495 44.1384 15.4083 44.4755 12.4803C44.6959 10.565 44.0406 8.68483 42.6777 7.32228C41.3143 5.95904 39.4343 5.30378 37.5194 5.52446C34.5904 5.86108 31.7503 4.68479 29.9186 2.37646C28.7205 0.86611 26.9277 0 25.0001 0C23.0723 0 21.2795 0.866111 20.0812 2.3763C18.2495 4.68445 15.4093 5.86108 12.481 5.52463C10.5657 5.30344 8.68551 5.95904 7.32245 7.32211C5.95921 8.68483 5.30395 10.565 5.52429 12.4806C5.86141 15.4081 4.68462 18.2493 2.37579 20.0814C0.866111 21.2797 0 23.0724 0 24.9999C0 26.9274 0.865941 28.7201 2.37613 29.9188C4.68462 31.7507 5.86141 34.5917 5.52429 37.5197C5.30395 39.435 5.95921 41.3152 7.32211 42.6777C8.68551 44.041 10.5671 44.6966 12.4805 44.4755C15.4083 44.1386 18.2495 45.3152 20.0812 47.6235C21.2794 49.1337 23.0721 50 25.0001 50C26.9277 50 28.7205 49.1339 29.9188 47.6237C31.7507 45.3155 34.5909 44.1391 37.519 44.4754C39.4345 44.6955 41.3145 44.041 42.6776 42.6779C44.0408 41.3152 44.696 39.435 44.4757 37.5195C44.1386 34.5919 45.3155 31.7505 47.6242 29.9186C49.1339 28.7203 50 26.9276 50 24.9999C50 23.0723 49.1337 21.2797 47.6237 20.0812Z"
        fill="url(#paint0_linear_871_20)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_871_20"
          x1="-3.30219"
          y1="55.9516"
          x2="58.1146"
          y2="41.6659"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#29FBFB" />
          <stop offset="0.850962" stop-color="#07FFE6" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default Percent; 
