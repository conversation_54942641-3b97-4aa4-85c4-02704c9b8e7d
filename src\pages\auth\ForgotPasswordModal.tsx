import React, { useState } from 'react';
import ReactDOM from 'react-dom';
// import axios from "axios";
import BaseHeader, { BaseUrl } from '../../api/BaseHeader';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Mail } from 'lucide-react';

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToLogin?: () => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({
  isOpen,
  onClose,
  onSwitchToLogin,
}) => {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState<{ email?: string }>({});
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleForgotPasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setLoading(true);

    const newErrors: { email?: string } = {};
    if (!email.trim()) newErrors.email = 'Vui lòng nhập email';

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setLoading(false);
      return;
    }

    try {
      await BaseHeader({
        method: 'post',
        url: '/forgot-password',
        baseURL: BaseUrl,
        data: { email },
      });
      toast.success('Email đặt lại mật khẩu đã được gửi!');
      setEmail(''); // Xóa email sau khi gửi
      if (onSwitchToLogin) {
        onSwitchToLogin(); // Quay lại form đăng nhập
      }
    } catch (err: any) {
      toast.error(
        err.response?.data?.message || 'Có lỗi xảy ra, vui lòng thử lại'
      );
    } finally {
      setLoading(false);
    }
  };

  return ReactDOM.createPortal(
    <div className="fixed inset-0 z-[10000] flex items-center justify-center backdrop-blur-sm bg-black/30 px-4">
      <div className="bg-white w-full max-w-5xl rounded-lg shadow-lg flex flex-col md:flex-row overflow-hidden relative">
        <button
          onClick={() => {
            setEmail('');
            setErrors({});
            onClose();
          }}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-xl"
        >
          ✕
        </button>

        <div className="hidden md:block w-1/2 bg-blue-700">
          <img
            src="/aka2.jpg"
            alt="Ảnh mô tả"
            className="w-full h-full object-cover"
          />
        </div>

        <div className="w-full md:w-1/2 p-8">
          <h2 className="text-2xl font-bold text-blue-600 mb-2">
            Quên mật khẩu
          </h2>
          <p className="text-sm text-gray-500 mb-6">
            Nhập email để nhận liên kết đặt lại mật khẩu
          </p>

          <form onSubmit={handleForgotPasswordSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-blue-600"
              >
                Email
              </label>
              <div className="relative mt-1">
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Email"
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring focus:ring-blue-400 pr-10"
                />
                <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              </div>
              {errors.email && (
                <p className="text-sm text-red-500 mt-1">{errors.email}</p>
              )}
            </div>

            <div className="text-right text-sm text-blue-600 hover:underline cursor-pointer">
              <span onClick={onSwitchToLogin}>Quay lại đăng nhập</span>
            </div>

            <button
              type="submit"
              disabled={loading}
              className={`w-full ${
                loading ? 'bg-blue-400' : 'bg-blue-500 hover:bg-blue-600'
              } text-white font-semibold py-2 rounded-lg transition flex justify-center items-center`}
            >
              {loading ? (
                <svg
                  className="animate-spin h-5 w-5 text-white"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                  />
                </svg>
              ) : (
                'Gửi yêu cầu'
              )}
            </button>
          </form>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ForgotPasswordModal;
