import React from 'react';
import { IconProps } from './types';

const FbIcon: React.FC<IconProps> = () => {
  return (
    <svg
      width="25"
      height="25"
      viewBox="0 0 25 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_739_2238)">
        <mask
          id="mask0_739_2238"
          style={{ maskType: 'luminance' }}
          maskUnits="userSpaceOnUse"
          x="-5"
          y="-5"
          width="35"
          height="35"
        >
          <path
            d="M-4.88965 -4.51345H29.3284V29.7046H-4.88965V-4.51345Z"
            fill="white"
          />
        </mask>
        <g mask="url(#mask0_739_2238)">
          <path
            d="M24.4405 12.5958C24.4405 5.84651 18.9691 0.375097 12.2198 0.375097C5.47044 0.375097 -0.000976562 5.84651 -0.000976562 12.5958C-0.000976562 18.3271 3.94461 23.1361 9.26752 24.4567V16.3303H6.74751V12.5958H9.26752V10.9866C9.26752 6.8271 11.1499 4.89916 15.2336 4.89916C16.0078 4.89916 17.3437 5.05094 17.8902 5.20277V8.58811C17.6018 8.55775 17.1008 8.54255 16.4784 8.54255C14.4745 8.54255 13.7003 9.30161 13.7003 11.2751V12.5958H17.692L17.0063 16.3303H13.7003V24.7271C19.7513 23.9962 24.4405 18.8439 24.4405 12.5958Z"
            fill="#0866FF"
          />
          <path
            d="M17.0058 16.3301L17.6916 12.5957H13.6999V11.2749C13.6999 9.30141 14.4741 8.54241 16.478 8.54241C17.1004 8.54241 17.6013 8.55756 17.8898 8.58792V5.20263C17.3433 5.0508 16.0074 4.89897 15.2332 4.89897C11.1495 4.89897 9.26708 6.82696 9.26708 10.9865V12.5957H6.74707V16.3301H9.26708V24.4565C10.2126 24.6911 11.2013 24.8164 12.2193 24.8164C12.7206 24.8164 13.2145 24.7855 13.6999 24.7269V16.3301H17.0058Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_739_2238">
          <rect
            width="24.4415"
            height="24.4415"
            fill="white"
            transform="translate(0 0.375)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default FbIcon;
