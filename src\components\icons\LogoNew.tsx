import React from 'react';
import { IconProps } from './types';

const LogoNew: React.FC<IconProps> = () => {
  return (
    <svg
      width="154"
      height="48"
      viewBox="0 0 154 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        y="0.375"
        width="46.7027"
        height="46.7027"
        rx="15.5676"
        fill="url(#paint0_linear_739_2161)"
      />
      <g filter="url(#filter0_d_739_2161)">
        <path
          d="M21.5559 16.2363L7.74521 28.452C6.89104 29.0888 6.37057 30.1481 6.50834 31.3237C6.67367 32.7198 7.79114 33.8464 9.18415 34.0209C10.5863 34.1985 11.8324 33.4331 12.3835 32.2759C12.6009 31.8166 12.8182 31.3604 13.0723 30.9196L21.5498 16.2363H21.5559Z"
          fill="white"
        />
        <path
          d="M17.3341 32.9187L35.7708 32.8575C36.8302 32.7656 37.9231 33.2126 38.5997 34.1801C39.4049 35.3312 39.3192 36.9171 38.3946 37.9733C37.4639 39.0388 36.025 39.2959 34.8463 38.7999C34.3778 38.604 33.9125 38.405 33.4288 38.2458L17.3311 32.9187H17.3341Z"
          fill="white"
        />
        <path
          d="M33.8174 28.3295L24.5256 12.4032C23.9133 11.5307 23.751 10.3642 24.25 9.28963C24.844 8.01602 26.2584 7.29655 27.6361 7.56597C29.023 7.83845 29.969 8.95286 30.1282 10.2234C30.1925 10.7255 30.2538 11.2307 30.3579 11.7266L33.8113 28.3264L33.8174 28.3295Z"
          fill="white"
        />
        <path
          d="M28.5608 28.3355L24.1706 20.7336L19.7803 28.3355H28.5608Z"
          fill="white"
        />
      </g>
      <path
        d="M57.6822 31.7263L63.6487 15.4964H68.0123L73.9566 31.7263H70.6394L66.3203 19.7487L65.8751 17.9231H65.697L65.2739 19.7487L60.9549 31.7263H57.6822ZM61.8899 28.2532L62.7359 25.782H68.8806L69.7266 28.2532H61.8899ZM75.4518 31.7263V15.4964H78.6799V23.0436L85.8932 15.4964H89.9006L83.2439 22.1754L90.0565 31.7263H86.1159L80.884 24.4685L78.6799 26.7171V31.7263H75.4518ZM90.9475 31.7263L96.9141 15.4964H101.278L107.222 31.7263H103.905L99.5857 19.7487L99.1404 17.9231H98.9623L98.5393 19.7487L94.2202 31.7263H90.9475ZM95.1553 28.2532L96.0013 25.782H102.146L102.992 28.2532H95.1553ZM111.472 31.7263L117.439 15.4964H121.803L127.747 31.7263H124.43L120.111 19.7487L119.665 17.9231H119.487L119.064 19.7487L114.745 31.7263H111.472ZM115.68 28.2532L116.526 25.782H122.671L123.517 28.2532H115.68ZM133.107 31.9267C132.335 31.9267 131.638 31.8005 131.014 31.5482C130.406 31.2959 129.879 30.9397 129.434 30.4796C128.988 30.0195 128.647 29.4629 128.41 28.8098C128.172 28.1568 128.053 27.4369 128.053 26.6503V24.936C128.053 23.8822 128.261 22.962 128.677 22.1754C129.092 21.3739 129.679 20.7579 130.435 20.3275C131.192 19.8822 132.083 19.6596 133.107 19.6596C133.968 19.6596 134.732 19.8303 135.4 20.1717C136.083 20.4982 136.588 20.9583 136.914 21.552H137.048V15.4964H140.165V31.7263H137.337L137.048 30.012H136.914C136.588 30.6206 136.083 31.0955 135.4 31.4369C134.732 31.7634 133.968 31.9267 133.107 31.9267ZM134.198 29.6113C134.851 29.6113 135.422 29.4629 135.912 29.166C136.417 28.8692 136.795 28.4536 137.048 27.9193V23.6447C136.795 23.1253 136.417 22.7171 135.912 22.4203C135.422 22.1234 134.851 21.975 134.198 21.975C133.604 21.975 133.092 22.0863 132.662 22.3089C132.231 22.5167 131.89 22.821 131.638 23.2217C131.4 23.6076 131.281 24.09 131.281 24.6689V26.9174C131.281 27.4666 131.4 27.949 131.638 28.3646C131.89 28.7801 132.231 29.0918 132.662 29.2996C133.092 29.5074 133.604 29.6113 134.198 29.6113ZM146.888 31.949C145.716 31.949 144.729 31.7931 143.927 31.4814C143.14 31.1697 142.532 30.7319 142.101 30.1679C141.671 29.6039 141.419 28.9582 141.345 28.231H144.461C144.491 28.6317 144.61 28.9657 144.818 29.2328C145.025 29.5 145.307 29.7004 145.664 29.8339C146.02 29.9675 146.443 30.0343 146.933 30.0343C147.675 30.0343 148.224 29.9081 148.58 29.6558C148.936 29.3887 149.114 29.0399 149.114 28.6095C149.114 28.1493 148.944 27.808 148.602 27.5853C148.276 27.3479 147.793 27.1475 147.155 26.9842L145.285 26.628C144.424 26.4351 143.727 26.1902 143.192 25.8933C142.673 25.5816 142.294 25.2106 142.057 24.7802C141.819 24.3349 141.701 23.8303 141.701 23.2663C141.701 22.4796 141.923 21.8192 142.369 21.2848C142.814 20.7505 143.415 20.3424 144.172 20.0603C144.944 19.7783 145.812 19.6373 146.777 19.6373C147.801 19.6373 148.691 19.7783 149.448 20.0603C150.22 20.3424 150.829 20.7505 151.274 21.2848C151.734 21.8043 152.001 22.4351 152.075 23.1772H149.092C149.062 22.7913 148.944 22.4796 148.736 22.2422C148.528 21.9898 148.254 21.8117 147.912 21.7078C147.586 21.5891 147.215 21.5297 146.799 21.5297C146.369 21.5297 145.998 21.5891 145.686 21.7078C145.389 21.8117 145.159 21.9676 144.996 22.1754C144.847 22.3683 144.773 22.6058 144.773 22.8878C144.773 23.2737 144.921 23.578 145.218 23.8006C145.53 24.0232 146.012 24.2162 146.665 24.3794L148.447 24.6689C149.218 24.8321 149.894 25.0622 150.472 25.359C151.051 25.641 151.504 26.0195 151.831 26.4944C152.157 26.9546 152.32 27.5557 152.32 28.2978C152.32 29.0992 152.09 29.7746 151.63 30.3237C151.17 30.858 150.532 31.2662 149.716 31.5482C148.899 31.8154 147.957 31.949 146.888 31.949Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_d_739_2161"
          x="5.12417"
          y="6.1476"
          width="37.983"
          height="36.8521"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dx="1.2973" dy="1.2973" />
          <feGaussianBlur stdDeviation="1.32973" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.152491 0 0 0 0 0.403709 0 0 0 0 0.666346 0 0 0 0.3 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_739_2161"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_739_2161"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_739_2161"
          x1="3.64808e-07"
          y1="42.8615"
          x2="50.5163"
          y2="47.1557"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#07FFC9" />
          <stop offset="0.463785" stop-color="#29FBFB" />
          <stop offset="0.94523" stop-color="#00EAFF" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default LogoNew;
